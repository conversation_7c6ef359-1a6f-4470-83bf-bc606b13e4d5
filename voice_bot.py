#!/usr/bin/env python3
"""
PolicyBazaar Voice Bot - Professional Outbound Call System

AI-Powered Natural Conversation System with Direct Audio Processing

Features:
- Dialer integration via WebSocket
- Natural conversation using Vertex AI Gemini 2.5 Flash
- Direct audio input processing (no separate STT required)
- Real-time intent detection through conversation
- Automatic agent connection for interested customers
- Graceful call ending for non-interested customers
- High-quality text-to-speech synthesis
"""

import asyncio
import base64
import json
import logging
import os
import re
import sys
import time
import traceback
from typing import Optional, Dict, Any

import websockets
from websockets.server import WebSocketServerProtocol
from google.cloud import texttospeech
from google.cloud import aiplatform
from vertexai.generative_models import GenerativeModel, Part
from pydub import AudioSegment
import webrtcvad
import io

# Import configuration
from config import (
    GOOGLE_CLOUD_PROJECT, SERVICE_ACCOUNT_FILE,
    TTS_CONFIG, AI_CONFIG,
    SERVER_CONFIG, LOGGING_CONFIG, ERROR_MESSAGES
)

# Configure logging
logging.basicConfig(
    level=getattr(logging, LOGGING_CONFIG["level"]),
    format=LOGGING_CONFIG["format"],
    handlers=[
        logging.FileHandler(LOGGING_CONFIG["file"]),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)


class ProfessionalVAD:
    """
    Professional Voice Activity Detection using WebRTC VAD
    
    Features:
    - High-quality speech detection (90%+ accuracy)
    - Noise robust detection
    - Real-time performance optimized
    - Multiple aggressiveness levels
    """
    
    def __init__(self, aggressiveness=2, sample_rate=8000, frame_duration_ms=20):
        """
        Initialize WebRTC VAD
        
        Args:
            aggressiveness (int): VAD aggressiveness level (0-3)
                0: Least aggressive (most permissive)
                1: Low aggressive 
                2: Moderate aggressive (balanced - recommended)
                3: Most aggressive (strict)
            sample_rate (int): Audio sample rate (8000, 16000, 32000, 48000)
            frame_duration_ms (int): Frame duration in ms (10, 20, 30)
        """
        self.vad = webrtcvad.Vad(aggressiveness)
        self.aggressiveness = aggressiveness  # Store for later reference
        self.sample_rate = sample_rate
        self.frame_duration_ms = frame_duration_ms
        self.frame_size = int(sample_rate * frame_duration_ms / 1000)  # Number of samples per frame
        self.frame_bytes = self.frame_size * 2  # 16-bit audio = 2 bytes per sample
        
        logger.info(f"🎤 WebRTC VAD initialized: aggressiveness={aggressiveness}, "
                   f"sample_rate={sample_rate}Hz, frame_duration={frame_duration_ms}ms")
        logger.info(f"📊 Frame size: {self.frame_size} samples ({self.frame_bytes} bytes)")
    
    def is_speech(self, audio_chunk: bytes) -> bool:
        """
        Detect if audio chunk contains speech
        
        Args:
            audio_chunk (bytes): Raw audio data (PCM 16-bit)
            
        Returns:
            bool: True if speech detected, False otherwise
        """
        try:
            # Ensure chunk is the correct size for VAD
            if len(audio_chunk) != self.frame_bytes:
                logger.debug(f"⚠️ VAD: Chunk size {len(audio_chunk)} != expected {self.frame_bytes}, padding/truncating")
                if len(audio_chunk) < self.frame_bytes:
                    # Pad with zeros if too short
                    audio_chunk = audio_chunk + b'\x00' * (self.frame_bytes - len(audio_chunk))
                else:
                    # Truncate if too long
                    audio_chunk = audio_chunk[:self.frame_bytes]
            
            # Use WebRTC VAD to detect speech
            return self.vad.is_speech(audio_chunk, self.sample_rate)
            
        except Exception as e:
            logger.error(f"❌ VAD error: {e}")
            # Fallback to simple energy detection if VAD fails
            return self._fallback_energy_detection(audio_chunk)
    
    def _fallback_energy_detection(self, audio_chunk: bytes) -> bool:
        """Fallback simple volume-based detection if VAD fails"""
        try:
            import struct
            samples = struct.unpack(f'<{len(audio_chunk)//2}h', audio_chunk)
            # Simple energy check - if most samples are just background noise (near zero), it's silence
            avg_energy = sum(abs(sample) for sample in samples) / len(samples)
            return avg_energy > 100  # Basic threshold for distinguishing speech from silence
        except:
            return False
    
    def get_aggressiveness_description(self) -> str:
        """Get human-readable aggressiveness description"""
        descriptions = {
            0: "Least aggressive (most permissive)",
            1: "Low aggressive", 
            2: "Moderate aggressive (balanced)",
            3: "Most aggressive (strict)"
        }
        return descriptions.get(self.aggressiveness, "Unknown")


class PolicyBazaarVoiceBot:
    """Professional Voice Bot for PolicyBazaar Outbound Calls"""
    
    def __init__(self):
        """Initialize the voice bot with all required services"""
        try:
            logger.info("🚀 Initializing PolicyBazaar Voice Bot...")
            
            # Initialize Google Cloud services
            self._initialize_google_services()
            
            # Initialize professional VAD for speech detection
            self.vad = ProfessionalVAD(
                aggressiveness=2,  # Balanced mode for production
                sample_rate=8000,  # Match dialer audio format
                frame_duration_ms=20  # 320 bytes = 20ms frames from dialer
            )
            
            # Initialize conversation tracking
            self.conversation_context = {}
            self.active_connections = 0
            self.total_connections = 0
            
            # Initialize session state tracking
            self.session_states = {}  # Track session states (active, agent_connected, ended)
            self.bot_states = {}  # Track bot states (speaking, listening, idle)
            
            logger.info("✅ PolicyBazaar Voice Bot initialized successfully")
            logger.info(f"🔊 VAD Mode: {self.vad.get_aggressiveness_description()}")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize voice bot: {e}")
            raise
    
    def _initialize_google_services(self):
        """Initialize Google Cloud services (TTS, Vertex AI)"""
        try:
            # Set Google Cloud credentials
            os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = SERVICE_ACCOUNT_FILE
            
            # Direct audio processing with Gemini 2.5 Flash - no separate STT needed
            
            # Initialize Text-to-Speech client
            self.tts_client = texttospeech.TextToSpeechClient()
            
            # Initialize Vertex AI for Gemini
            aiplatform.init(project=GOOGLE_CLOUD_PROJECT)
            self.gemini_model = GenerativeModel(AI_CONFIG["model"])
            
            logger.info("✅ Google Cloud services initialized")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Google Cloud services: {e}")
            raise
    
    
    async def generate_ai_response_from_audio(self, audio_data: bytes, session_id: str) -> str:
        """Generate AI response directly from audio using Gemini 2.5 Flash audio input"""
        try:
            # Get conversation history for this session
            history = self.conversation_context.get(session_id, [])
            
            # Create audio part for Gemini
            try:
                # Convert audio to WAV format for Gemini
                converted_audio = await self.convert_audio_format(audio_data)
                
                # Create audio part
                audio_part = Part.from_data(
                    data=converted_audio,
                    mime_type="audio/wav"
                )
                
                # Create contextual prompt for insurance conversations with audio input
                history_context = ""
                if len(history) > 0:
                    recent_history = history[-2:]  # Last 2 messages for context
                    history_context = "\n".join([f"{msg['role']}: {msg['content']}" for msg in recent_history])

                text_prompt = f"""You are a PolicyBazaar insurance assistant. Listen to the customer's audio message and respond conversationally and helpfully, but ONLY respond to insurance-related queries.

Recent conversation:
{history_context}

DOMAIN RESTRICTION (CRITICAL):
- You ONLY have knowledge about insurance products and services
- If customer asks about non-insurance topics (weather, sports, news, food, entertainment, technology, etc.), respond: "I'm sorry, but I only have knowledge related to insurance products and services. I can help you with health insurance, life insurance, motor insurance, travel insurance, and other PolicyBazaar offerings. How can I assist you with your insurance needs today?"
- NEVER provide information about topics outside of insurance domain

INSURANCE TOPICS YOU CAN HELP WITH:
- Health Insurance, Life Insurance, Motor/Car Insurance, Travel Insurance
- Insurance policies, premiums, coverage, claims, quotes
- PolicyBazaar services and offerings
- Connecting customers to insurance agents

INTENT DETECTION RULES:
- ONLY connect to agent if customer is CLEARLY interested and ready (asking for quotes, wanting to buy, sharing details)
- If customer shows clear interest, say: "Great! Let me connect you with one of our insurance experts who can help you find the perfect policy. Please hold on."
- If customer is NOT interested or wants to end call, say: "I understand. Thank you for your time. Have a great day!"
- For unclear/partial speech or general questions about insurance, continue conversation to understand their needs

POSITIVE INTENT (connect to agent):
- "I want to buy insurance"
- "Can you give me a quote?"
- "I'm interested, what are the prices?"
- Sharing personal details for quotes

NEGATIVE INTENT (end call):
- "Not interested", "No thanks", "Busy now"
- "Remove my number", "Don't call again"

NEUTRAL (continue conversation):
- General questions about insurance
- Unclear/partial speech
- Just asking "can you help me" without specific interest

Keep responses short (1-2 sentences). Ask specific questions to understand their insurance needs.

Listen to the audio and respond appropriately:"""
                
                # Generate response using Vertex AI with audio input
                logger.info(f"🎤 Calling Vertex AI Gemini with audio input (size: {len(converted_audio)} bytes)")
                logger.info(f"📋 Using conversation history with {len(history)} messages")
                
                response = self.gemini_model.generate_content(
                    [text_prompt, audio_part],
                    generation_config={
                        "temperature": AI_CONFIG["temperature"],
                        "max_output_tokens": AI_CONFIG["max_output_tokens"],
                        "top_p": AI_CONFIG["top_p"]
                    }
                )
                
                ai_response = response.text
                
                # Clean the response text for TTS (remove markdown formatting)
                ai_response = self._clean_ai_response(ai_response)
                
                logger.info(f"✅ ############## AI Response from Vertex AI (audio input): '{ai_response}'")
                
                # Update conversation history
                # Since we don't have the actual transcript, we'll use a placeholder
                history.append({"role": "user", "content": "[Audio message]"})
                history.append({"role": "assistant", "content": ai_response})
                self.conversation_context[session_id] = history[-6:]  # Keep last 6 messages
                
                return ai_response
                
            except Exception as ai_error:
                logger.error(f"❌ Vertex AI audio processing error: {ai_error}")
                # Fallback to a generic response
                ai_response = "I'm sorry, I couldn't process your message clearly. Could you please repeat what you'd like to know about our insurance products?"
                logger.info(f"✅ Fallback AI Response: '{ai_response}'")
                return ai_response
            
        except Exception as e:
            logger.error(f"❌ Error generating AI response from audio: {e}")
            logger.error(traceback.format_exc())
            return ERROR_MESSAGES["ai_generation"]

    def _clean_ai_response(self, ai_response: str) -> str:
        """Clean AI response for TTS (remove markdown formatting)"""
        # Remove markdown bold formatting (**text**)
        ai_response = re.sub(r'\*\*(.*?)\*\*', r'\1', ai_response)
        # Remove markdown italic formatting (*text*)
        ai_response = re.sub(r'\*(.*?)\*', r'\1', ai_response)
        # Remove any remaining markdown symbols
        ai_response = re.sub(r'#+\s*', '', ai_response)  # Remove headers
        ai_response = re.sub(r'`(.*?)`', r'\1', ai_response)  # Remove code blocks
        ai_response = re.sub(r'\[(.*?)\]\(.*?\)', r'\1', ai_response)  # Remove links
        # Clean up extra whitespace
        ai_response = re.sub(r'\n\s*\n', '\n', ai_response)  # Remove extra line breaks
        return ai_response.strip()



    # Audio Processing and TTS Functions
    
    async def text_to_speech(self, text: str) -> bytes:
        """Convert text to speech using Google TTS"""
        try:
            logger.info(f"🔊 Converting to speech: '{text}'")
            
            synthesis_input = texttospeech.SynthesisInput(text=text)
            
            # Create voice configuration
            voice = texttospeech.VoiceSelectionParams(
                language_code=TTS_CONFIG["language_code"],
                name=TTS_CONFIG["voice_name"],
                ssml_gender=getattr(texttospeech.SsmlVoiceGender, TTS_CONFIG["ssml_gender"])
            )
            
            # Create audio configuration
            audio_config = texttospeech.AudioConfig(
                audio_encoding=getattr(texttospeech.AudioEncoding, TTS_CONFIG["audio_encoding"]),
                sample_rate_hertz=TTS_CONFIG["sample_rate_hertz"]
            )
            
            response = self.tts_client.synthesize_speech(
                input=synthesis_input,
                voice=voice,
                audio_config=audio_config
            )
            
            logger.info(f"✅ TTS completed (size: {len(response.audio_content)} bytes)")
            return response.audio_content
            
        except Exception as e:
            logger.error(f"❌ Error in text-to-speech: {e}")
            return None

    async def send_interrupt_signal(self, websocket: WebSocketServerProtocol, session_id: str):
        """
        Send interrupt signal to dialer to stop current TTS playback
        """
        try:
            interrupt_signal = {
                'type': 'signal',
                'data': '__INTERRUPT__',
                'details': 'Customer interrupted - stopping TTS playback'
            }
            await websocket.send(json.dumps(interrupt_signal))
            logger.info(f"🛑 Sent __INTERRUPT__ signal to dialer for {session_id}")
            
            # Change bot state to listening
            self.bot_states[session_id] = "listening"
            
            # Cancel any pending TTS completion timer
            if hasattr(self, '_tts_timers') and session_id in self._tts_timers:
                self._tts_timers[session_id].cancel()
                del self._tts_timers[session_id]
                logger.debug(f"🕒 Cancelled TTS completion timer for {session_id}")
            
        except Exception as e:
            logger.error(f"❌ Error sending interrupt signal: {e}")

    def _calculate_audio_duration(self, audio_data: bytes) -> float:
        """
        Calculate approximate duration of audio data in seconds
        
        Args:
            audio_data: Raw audio bytes (8kHz, 16-bit, mono)
            
        Returns:
            float: Duration in seconds
        """
        try:
            # Audio format: 8kHz, 16-bit (2 bytes), mono
            # Duration = total_bytes / (sample_rate * bytes_per_sample * channels)
            duration = len(audio_data) / (8000 * 2 * 1)
            return duration
        except Exception as e:
            logger.error(f"❌ Error calculating audio duration: {e}")
            return 3.0  # Default fallback duration

    async def _schedule_tts_completion(self, session_id: str, audio_duration: float):
        """
        Schedule bot state reset to 'listening' after TTS playback completes
        """
        try:
            # Initialize timers dict if not exists
            if not hasattr(self, '_tts_timers'):
                self._tts_timers = {}
            
            # Cancel any existing timer for this session
            if session_id in self._tts_timers:
                self._tts_timers[session_id].cancel()
            
            # Add small buffer time for network latency and dialer processing
            completion_delay = audio_duration + 0.5  # Add 500ms buffer
            
            async def reset_to_listening():
                try:
                    await asyncio.sleep(completion_delay)
                    if session_id in self.bot_states:
                        self.bot_states[session_id] = "listening"
                        logger.info(f"🔄 Bot state reset to 'listening' after TTS completion for {session_id}")
                    
                    # Clean up timer reference
                    if hasattr(self, '_tts_timers') and session_id in self._tts_timers:
                        del self._tts_timers[session_id]
                        
                except asyncio.CancelledError:
                    logger.debug(f"🕒 TTS completion timer cancelled for {session_id}")
                except Exception as e:
                    logger.error(f"❌ Error in TTS completion timer: {e}")
            
            # Create and store the timer task
            timer_task = asyncio.create_task(reset_to_listening())
            self._tts_timers[session_id] = timer_task
            
            logger.debug(f"🕒 Scheduled bot state reset in {completion_delay:.1f}s for {session_id}")
            
        except Exception as e:
            logger.error(f"❌ Error scheduling TTS completion: {e}")

    async def _handle_intent_actions(self, websocket: WebSocketServerProtocol, ai_response: str, session_id: str):
        """
        Handle intent-based actions (agent connection, call ending)
        """
        try:
            ai_response_lower = ai_response.lower()
            
            # Check if customer shows positive intent (connect to agent)
            if ("connect you with" in ai_response_lower or 
                "insurance expert" in ai_response_lower or
                "please hold on" in ai_response_lower):
                logger.info(f"🎯 POSITIVE INTENT detected - connecting to agent")
                
                # Set session state to prevent further audio processing
                self.session_states[session_id] = "agent_connected"
                self.bot_states[session_id] = "idle"
                logger.info(f"🔒 Session {session_id} marked as 'agent_connected' - stopping audio processing")
                
                # Send connect to agent signal
                agent_signal = {
                    'type': 'signal',
                    'data': '__CONNECT_AGENT__',
                    'details': 'Customer interested - connecting to human agent'
                }
                await websocket.send(json.dumps(agent_signal))
                logger.info(f"👨‍💼 Sent connect agent signal for {session_id}")
                return
            
            # Check if customer shows negative intent (end call gracefully)
            elif ("thank you for your time" in ai_response_lower or
                  "have a great day" in ai_response_lower or
                  "not interested" in ai_response_lower):
                logger.info(f"🚫 NEGATIVE INTENT detected - ending call gracefully")
                
                # Set session state to prevent further audio processing
                self.session_states[session_id] = "ended"
                self.bot_states[session_id] = "idle"
                logger.info(f"🔒 Session {session_id} marked as 'ended' - stopping audio processing")
                
                # Send session end signal
                end_signal = {
                    'type': 'signal',
                    'data': '__SESSION_END__',
                    'details': 'Customer not interested - call ended gracefully'
                }
                await websocket.send(json.dumps(end_signal))
                logger.info(f"🔚 Sent session end signal for {session_id}")
                return
                
        except Exception as e:
            logger.error(f"❌ Error handling intent actions: {e}")



    async def convert_audio_format(self, audio_data: bytes) -> bytes:
        """Convert audio to WAV format suitable for Gemini audio processing"""
        try:
            # Convert 8kHz dialer audio to 16kHz for better AI processing
            audio = AudioSegment.from_raw(
                io.BytesIO(audio_data),
                sample_width=2,  # 16-bit
                frame_rate=8000,  # 8kHz from dialer
                channels=1        # Mono
            )
            
            # Resample to 16kHz for optimal Gemini performance
            audio = audio.set_frame_rate(16000)
            
            # Export as WAV
            output = io.BytesIO()
            audio.export(output, format="wav")
            converted_audio = output.getvalue()
            
            logger.info(f"✅ Converted dialer audio for Gemini: {len(audio_data)} -> {len(converted_audio)} bytes")
            return converted_audio
            
        except Exception as e:
            logger.error(f"❌ Error converting audio format: {e}")
            # Return original audio if conversion fails
            return audio_data



    async def _process_dialer_audio(self, websocket: WebSocketServerProtocol, session_id: str, audio_chunk: bytes):
        """Process dialer audio with real-time interruption handling"""
        try:
            # Initialize buffers and tracking
            if not hasattr(self, '_dialer_buffers'):
                self._dialer_buffers = {}
                self._dialer_last_flush = {}
                self._dialer_last_response = {}
                self._dialer_speech_start = {}

            # Check session state - don't process if agent connected or session ended
            session_state = self.session_states.get(session_id, "active")
            if session_state in ["agent_connected", "ended"]:
                logger.info(f"🚫 Session {session_id} state is '{session_state}', ignoring audio")
                return

            # Initialize bot state if not set
            if session_id not in self.bot_states:
                self.bot_states[session_id] = "listening"

            bot_state = self.bot_states[session_id]

            # INTERRUPTION HANDLING: If bot is speaking, detect customer speech and send interrupt
            if bot_state == "speaking":
                # Use VAD to detect if customer is actually speaking (not just noise)
                is_speech = self.vad.is_speech(audio_chunk)
                if is_speech:
                    logger.info(f"🛑 Customer speech detected while bot speaking - sending interrupt to dialer")
                    await self.send_interrupt_signal(websocket, session_id)
                    # After interrupt, we'll continue to process this audio chunk normally
                    # Fall through to normal processing logic below
                else:
                    logger.debug(f"🔇 Background noise while bot speaking - ignoring")
                    return

            # NORMAL PROCESSING: Bot is listening, process customer audio normally
            buf = self._dialer_buffers.get(session_id, b'') + audio_chunk
            self._dialer_buffers[session_id] = buf

            last_flush = self._dialer_last_flush.get(session_id, 0)
            last_response = self._dialer_last_response.get(session_id, 0)
            now = time.time()

            # Cooldown period - but only when bot is not speaking (prevents feedback loops)
            if now - last_response < 3.0 and bot_state == "listening":
                # Only log once per cooldown period to avoid spam
                if not hasattr(self, '_cooldown_logged'):
                    self._cooldown_logged = {}
                if session_id not in self._cooldown_logged or now - self._cooldown_logged[session_id] > 1.0:
                    logger.info(f"🔇 Cooldown period active (3s after response), buffering speech...")
                    self._cooldown_logged[session_id] = now
                return

            # Use WebRTC VAD for professional speech detection
            is_speech = self.vad.is_speech(audio_chunk)
            
            # Initialize logging tracking
            if not hasattr(self, '_vad_logged'):
                self._vad_logged = {}
            
            # Initialize silence tracking
            if not hasattr(self, '_silence_chunks'):
                self._silence_chunks = {}
            if session_id not in self._silence_chunks:
                self._silence_chunks[session_id] = 0
            
            # Track speech start and silence detection using WebRTC VAD
            if is_speech and session_id not in self._dialer_speech_start:
                self._dialer_speech_start[session_id] = now
                logger.info(f"🎤 Speech detected by VAD, starting collection...")
            
            # Count silence chunks for endpoint detection using VAD results
            if not is_speech:  # Silence detected by VAD
                self._silence_chunks[session_id] += 1
                # Log silence detection occasionally for debugging
                if self._silence_chunks[session_id] % 25 == 0:  # Every 500ms of silence
                    silence_duration = self._silence_chunks[session_id] * 0.02
                    logger.info(f"🔇 VAD Silence detected: {silence_duration:.1f}s")
            else:  # Speech detected by VAD, reset silence counter
                if self._silence_chunks[session_id] > 0:  # Log when silence ends
                    final_silence = self._silence_chunks[session_id] * 0.02
                    logger.info(f"🎤 VAD Speech resumed after {final_silence:.1f}s silence")
                self._silence_chunks[session_id] = 0
            
            # Skip processing if no speech has started yet
            if session_id not in self._dialer_speech_start:
                return

            # Dynamic speech endpoint detection for natural conversation flow
            speech_start = self._dialer_speech_start.get(session_id, now)
            
            # More natural endpoint detection
            silence_duration = self._silence_chunks[session_id] * 0.02  # 20ms per chunk
            speech_duration = now - speech_start
            
            # VAD-optimized endpoint detection for natural conversations (stricter requirements)
            should_flush = (
                # Minimum speech requirements - increased for better quality
                (speech_duration >= 2.0 and silence_duration >= 0.8 and len(buf) >= 32000) or  # 2.0s speech + 800ms silence
                # Normal sentence processing
                (speech_duration >= 2.5 and silence_duration >= 0.5 and len(buf) >= 40000) or  # 2.5s speech + 500ms silence  
                # Longer utterances
                (speech_duration >= 3.0 and silence_duration >= 0.3 and len(buf) >= 48000) or  # 3.0s speech + 300ms silence
                # Force processing for very long speech
                (len(buf) >= 80000) or  # 5.0 seconds maximum buffer (increased for better quality)
                # Timeout fallbacks - increased minimum requirements
                (now - speech_start >= 4.0 and len(buf) >= 32000) or  # 4.0s timeout with 2s minimum
                (now - last_flush >= 5.0 and len(buf) >= 32000)  # 5.0s absolute timeout with 2s minimum
            )

            # Only process if we actually have speech detected (not just silence) - stricter requirements
            if should_flush and len(buf) >= 32000 and session_id in self._dialer_speech_start:  # Minimum 2.0 seconds + speech detected
                try:
                    logger.info(f"🎯 Processing audio buffer: {len(buf)} bytes ({len(buf)/16000:.1f}s)")
                    logger.info(f"📊 Speech duration: {speech_duration:.1f}s, Silence duration: {silence_duration:.1f}s")
                    logger.info(f"🎤 VAD detected speech: {is_speech}")
                    
                    # Generate AI response directly from audio using Gemini 2.5 Flash
                    
                    # Generate AI response directly from audio
                    ai_response = await self.generate_ai_response_from_audio(buf, session_id)
                    
                    if ai_response:
                        # Convert AI response to speech
                        response_audio = await self.text_to_speech(ai_response)
                        
                        if response_audio:
                            # Send complete TTS audio to dialer (professional approach)
                            response_message = {
                                'type': 'audio',
                                'data': base64.b64encode(response_audio).decode('utf-8'),
                                'transcript': '[Audio processed directly by Gemini]',
                                'confidence': 1.0  # Gemini processes audio directly
                            }
                            
                            # Set bot state to speaking before sending
                            self.bot_states[session_id] = "speaking"
                            
                            await websocket.send(json.dumps(response_message))
                            logger.info(f"📤 Sent complete TTS audio to dialer {session_id}")
                            
                            # Calculate audio duration and schedule state reset
                            audio_duration = self._calculate_audio_duration(response_audio)
                            await self._schedule_tts_completion(session_id, audio_duration)
                            logger.info(f"🕒 TTS duration: {audio_duration:.1f}s, will reset to listening automatically")
                            
                            # Update tracking
                            self._dialer_last_response[session_id] = time.time()
                            logger.info(f"🕒 Cooldown period (3s) started for {session_id}")
                            
                            # Handle intent-based actions
                            await self._handle_intent_actions(websocket, ai_response, session_id)
                        else:
                            logger.warning(f"⚠️ Failed to convert AI response to speech for {session_id}")
                    else:
                        logger.warning(f"⚠️ No AI response generated for {session_id}")
                        # Clear speech start tracking and silence chunks if no response
                        if session_id in self._dialer_speech_start:
                            del self._dialer_speech_start[session_id]
                        if session_id in self._silence_chunks:
                            del self._silence_chunks[session_id]
                    
                    # Clear buffer after processing
                    self._dialer_buffers[session_id] = b''
                    self._dialer_last_flush[session_id] = now
                    # Clear speech start tracking and silence chunks after processing
                    if session_id in self._dialer_speech_start:
                        del self._dialer_speech_start[session_id]
                    if session_id in self._silence_chunks:
                        del self._silence_chunks[session_id]
                    
                except Exception as e:
                    logger.error(f"❌ Error processing dialer audio: {e}")
                    # Clear buffer on error to prevent accumulation
                    self._dialer_buffers[session_id] = b''
                    self._dialer_last_flush[session_id] = now
                    # Clear speech start tracking and silence chunks on error
                    if session_id in self._dialer_speech_start:
                        del self._dialer_speech_start[session_id]
                    if session_id in self._silence_chunks:
                        del self._silence_chunks[session_id]
                    
        except Exception as e:
            logger.error(f"❌ Error in _process_dialer_audio: {e}")

    # WebSocket Server and Connection Management
    
    async def handle_websocket(self, websocket: WebSocketServerProtocol, path: str):
        """Handle WebSocket connections from dialer"""
        # Only accept connections to /ws endpoint
        if path != "/ws":
            logger.warning(f"⚠️ Rejected connection attempt to {path}, only /ws is supported")
            await websocket.close(1008, "Only /ws endpoint is supported")
            return
            
        session_id = f"dialer_{int(time.time() * 1000)}"
        
        try:
            logger.info(f"🔌 New WebSocket connection from dialer: {session_id}")
            self.active_connections += 1
            self.total_connections += 1
            
            # Initialize conversation context for this session
            if session_id not in self.conversation_context:
                self.conversation_context[session_id] = []
            
            # Initialize session state as active
            self.session_states[session_id] = "active"
            self.bot_states[session_id] = "listening"
            logger.info(f"🔓 Session {session_id} state set to 'active', bot state set to 'listening'")
            
            logger.info(f"⏳ Waiting for start event from dialer {session_id}...")
            
            # Process incoming messages
            async for message in websocket:
                try:
                    data = json.loads(message)
                    message_type = data.get('type')
                    
                    # Handle start event from dialer (session initialization)
                    if message_type == 'start':
                        # Extract customer information from start event
                        start_data = data.get('start', {})
                        user_name = start_data.get('user_name', 'Customer')
                        flow_name = start_data.get('flow_name', 'unknown')
                        call_sid = start_data.get('call_sid', session_id)
                        
                        logger.info(f"🚀 START EVENT RECEIVED for {session_id}")
                        logger.info(f"👤 Customer: {user_name}, Flow: {flow_name}, Call ID: {call_sid}")
                        
                        # Send personalized greeting after receiving start event
                        greeting_text = f"Hello! I am your PolicyBazaar voice assistant. How can I help you today?"
                        greeting_audio = await self.text_to_speech(greeting_text)
                        
                        if greeting_audio:
                            # Send complete greeting audio to dialer (professional approach)
                            greeting_message = {
                                'type': 'audio',
                                'data': base64.b64encode(greeting_audio).decode('utf-8'),
                                'transcript': greeting_text
                            }
                            
                            # Set bot state to speaking before sending greeting
                            self.bot_states[session_id] = "speaking"
                            
                            await websocket.send(json.dumps(greeting_message))
                            logger.info(f"📤 GREETING SENT: Sent complete greeting audio to dialer {session_id}")
                            
                            # Calculate greeting duration and schedule state reset
                            greeting_duration = self._calculate_audio_duration(greeting_audio)
                            await self._schedule_tts_completion(session_id, greeting_duration)
                            logger.info(f"🕒 Greeting duration: {greeting_duration:.1f}s, will reset to listening automatically")
                            
                            # Set last response time for cooldown period after greeting
                            if not hasattr(self, '_dialer_last_response'):
                                self._dialer_last_response = {}
                            self._dialer_last_response[session_id] = time.time()
                            logger.info(f"🕒 Cooldown period started (3s) after greeting for {session_id}")
                            
                            # Initialize conversation history with greeting
                            self.conversation_context[session_id].append({"role": "assistant", "content": greeting_text})
                        else:
                            logger.warning(f"⚠️ Failed to generate greeting audio for {session_id}")
                    
                    # Handle audio messages from dialer (main conversation flow)
                    elif message_type == 'audio':
                        # Extract audio data
                        audio_data = base64.b64decode(data.get('data', ''))
                        
                        if audio_data:
                            # Process dialer audio
                            await self._process_dialer_audio(websocket, session_id, audio_data)
                        else:
                            logger.warning(f"⚠️ Empty audio data from dialer {session_id}")
                    
                    elif message_type == 'ping':
                        # Handle ping for connection health
                        await websocket.send(json.dumps({'type': 'pong'}))
                    
                    elif message_type == 'signal':
                        # Handle signal messages from dialer
                        signal_data = data.get('data', '')
                        if signal_data == '__SESSION_END__':
                            logger.info(f"🔚 Session end signal received from dialer {session_id}")
                            break
                        elif signal_data == '__INTERRUPT__':
                            logger.info(f"⏸️ Interrupt signal received from dialer {session_id}")
                            # Could implement interrupt handling here if needed
                        else:
                            logger.info(f"📡 Signal received from dialer {session_id}: {signal_data}")
                    
                    elif message_type == 'close':
                        # Handle close request
                        logger.info(f"🔌 Dialer requested close for session {session_id}")
                        break
                    
                    else:
                        logger.warning(f"⚠️ Unknown message type: '{message_type}' in data: {json.dumps(data)}")
                        
                except json.JSONDecodeError as e:
                    logger.error(f"❌ Invalid JSON from dialer: {e}")
                except Exception as e:
                    logger.error(f"❌ Error processing message: {e}")
                    
        except websockets.exceptions.ConnectionClosed:
            logger.info(f"🔌 WebSocket connection closed by dialer: {session_id}")
        except Exception as e:
            logger.error(f"❌ WebSocket error: {e}")
        finally:
            self.active_connections -= 1
            logger.info(f"🔌 WebSocket connection ended: {session_id}")
    
    async def start_server(self):
        """Start the WebSocket server for dialer integration"""
        try:
            logger.info(f"🚀 Starting WebSocket server on {SERVER_CONFIG['websocket']['host']}:{SERVER_CONFIG['websocket']['port']}/ws")
            
            # Start WebSocket server on /ws endpoint
            server = await websockets.serve(
                self.handle_websocket,
                SERVER_CONFIG['websocket']['host'],
                SERVER_CONFIG['websocket']['port'],
                process_request=self._process_request
            )
            
            logger.info("✅ WebSocket server started successfully")
            logger.info("🔄 Waiting for dialer connections on /ws endpoint...")
            
            # Keep server running
            await server.wait_closed()
            
        except Exception as e:
            logger.error(f"❌ Failed to start server: {e}")
            raise
    
    def _process_request(self, path, headers):
        """Process WebSocket requests to only accept /ws endpoint"""
        if path == "/ws":
            return None  # Continue with WebSocket handshake
        else:
            logger.warning(f"⚠️ Rejected connection attempt to {path}, only /ws is supported")
            # Return HTTP 404 response for non-/ws paths
            from http import HTTPStatus
            return (HTTPStatus.NOT_FOUND, {"Content-Type": "text/plain"}, b"Not Found")


# Application Entry Point

async def main():
    """Main function to start the voice bot"""
    try:
        logger.info("🎤 PolicyBazaar Voice Bot - AI-Powered Outbound Call System")
        logger.info("🤖 Natural conversations using Vertex AI Gemini")
        
        # Create voice bot instance
        voice_bot = PolicyBazaarVoiceBot()
        logger.info("✅ Voice bot instance created successfully")
        
        # Start server
        await voice_bot.start_server()
        
    except KeyboardInterrupt:
        logger.info("🛑 Server stopped by user")
    except Exception as e:
        logger.error(f"❌ Fatal error: {e}")
        logger.error(traceback.format_exc())
        sys.exit(1)


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("🛑 Voice bot stopped")
    except Exception as e:
        logger.error(f"❌ Failed to start voice bot: {e}")
        sys.exit(1) 