"""
Configuration file for PolicyBazaar Voice Bot

Professional AI-powered voice bot with direct audio processing using Gemini 2.5 Flash
"""

# Google Cloud Configuration
GOOGLE_CLOUD_PROJECT = "named-defender-182511"
SERVICE_ACCOUNT_FILE = "credentials/google-service-account.json"

# Text-to-Speech Configuration
TTS_CONFIG = {
    "language_code": "en-IN",
    "voice_name": "en-IN-Standard-A",
    "ssml_gender": "FEMALE",
    "audio_encoding": "LINEAR16",
    "sample_rate_hertz": 8000,  # 8kHz for dialer compatibility
}

# AI Configuration - Gemini 2.5 Flash with direct audio processing
AI_CONFIG = {
    "model": "gemini-2.5-flash",
    "temperature": 0.7,
    "max_output_tokens": 1024,
    "top_p": 0.8,
}

# Server Configuration
SERVER_CONFIG = {
    "websocket": {
        "host": "0.0.0.0",  # Production host
        "port": 8766,        # WebSocket port for dialer
        "ping_interval": 20,
        "ping_timeout": 20,
    },
}

# Logging Configuration
LOGGING_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file": "logs/voice_bot.log",
    "max_bytes": ********,  # 10MB
    "backup_count": 5,
}

# Error Messages
ERROR_MESSAGES = {
    "ai_generation": "I apologize, but I'm having trouble generating a response. Please try again.",
} 