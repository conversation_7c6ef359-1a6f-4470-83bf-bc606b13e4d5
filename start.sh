#!/bin/bash

# PolicyBazaar Voice Bot - Production Startup Script
# This script sets up the environment and starts the voice bot

set -e  # Exit on any error

echo "🚀 Starting PolicyBazaar Voice Bot..."

# Check if uv is available
if command -v uv &> /dev/null; then
    echo "✅ Using uv package manager"
    export PATH="$HOME/.local/bin:$PATH"
    
    # Activate virtual environment
    if [ -d ".venv" ]; then
        echo "🔧 Activating virtual environment..."
        source .venv/bin/activate
    else
        echo "🔧 Creating virtual environment..."
        uv venv
        source .venv/bin/activate
        echo "📦 Installing dependencies..."
        uv sync
    fi
else
    echo "❌ Error: uv is required but not found!"
    echo "Please install uv first:"
    echo "curl -LsSf https://astral.sh/uv/install.sh | sh"
    exit 1
fi

# Check if Google Cloud credentials exist
if [ ! -f "credentials/google-service-account.json" ]; then
    echo "❌ Error: credentials/google-service-account.json not found!"
    echo "Please ensure your Google Cloud credentials are in place."
    exit 1
fi

# Set environment variables
export GOOGLE_APPLICATION_CREDENTIALS="$(pwd)/credentials/google-service-account.json"
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

echo "✅ Environment setup complete"
echo "🌐 Starting WebSocket server on port 8766..."
echo "🔌 Dialer endpoint: ws://0.0.0.0:8766/ws"
echo "📋 Press Ctrl+C to stop the server"

# Start the voice bot
python3 voice_bot.py
