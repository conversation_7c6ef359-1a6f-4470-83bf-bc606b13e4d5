[project]
name = "policybazaar-voice-bot"
version = "1.0.0"
description = "Professional Voice Bot for PolicyBazaar Outbound Calls"
authors = [
    {name = "PolicyBazaar Team", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.8.1"
license = {text = "Proprietary"}
keywords = ["voice-bot", "insurance", "dialer", "speech-recognition", "ai"]

dependencies = [
    "google-cloud-speech>=2.0.0",
    "google-cloud-texttospeech>=2.0.0",
    "google-cloud-aiplatform>=1.0.0",
    "vertexai>=1.0.0",
    "pydub>=0.25.0",
    "websockets==12.0",
    "webrtcvad>=2.0.10",
    "setuptools>=75.3.2",
]

[project.scripts]
voice-bot = "voice_bot:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["."]
include = [
    "voice_bot.py",
    "config.py",
    "dialer.py",
    "README.md",
    "start.sh",
    ".gitignore",
    "credentials/",
    "logs/",
]
