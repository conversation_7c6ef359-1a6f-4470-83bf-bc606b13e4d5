# Credentials Directory

This directory contains sensitive configuration files for the voice bot system.

## Files

### `google-service-account.json`
- **Purpose**: Google Cloud service account credentials
- **Usage**: Required for Google Cloud APIs (Speech-to-Text, Text-to-Speech, Vertex AI)
- **Security**: ⚠️ **<PERSON>VE<PERSON> commit this file to version control**

## Setup Instructions

### 1. Google Cloud Service Account

1. **Create a Google Cloud Project** (if you don't have one)
2. **Enable Required APIs**:
   - Speech-to-Text API
   - Text-to-Speech API
   - Vertex AI API

3. **Create Service Account**:
   ```bash
   # Using gcloud CLI
   gcloud iam service-accounts create voice-bot-sa \
     --display-name="Voice Bot Service Account"
   ```

4. **Grant Required Permissions**:
   ```bash
   gcloud projects add-iam-policy-binding YOUR_PROJECT_ID \
     --member="serviceAccount:voice-bot-sa@YOUR_PROJECT_ID.iam.gserviceaccount.com" \
     --role="roles/speech.client"
   
   gcloud projects add-iam-policy-binding YOUR_PROJECT_ID \
     --member="serviceAccount:voice-bot-sa@YOUR_PROJECT_ID.iam.gserviceaccount.com" \
     --role="roles/aiplatform.user"
   ```

5. **Download Service Account Key**:
   ```bash
   gcloud iam service-accounts keys create google-service-account.json \
     --iam-account=voice-bot-sa@YOUR_PROJECT_ID.iam.gserviceaccount.com
   ```

6. **Move to Credentials Directory**:
   ```bash
   mv google-service-account.json credentials/
   ```

### 2. Manual Setup

If you already have a service account key:
1. Rename your existing key file to `google-service-account.json`
2. Place it in this `credentials/` directory
3. Update `config.py` if your project ID is different

## Security Best Practices

### ✅ Do:
- Keep credentials in a separate directory
- Use environment variables for sensitive data
- Rotate service account keys regularly
- Use least-privilege permissions

### ❌ Don't:
- Commit credentials to version control
- Share credentials publicly
- Use production credentials in development
- Store credentials in application code

## Environment Variables

The application uses these environment variables:
```bash
export GOOGLE_APPLICATION_CREDENTIALS="credentials/google-service-account.json"
export PYTHONPATH="${PYTHONPATH}:$(pwd)"
```

## Troubleshooting

### Common Issues:

1. **"Could not automatically determine credentials"**
   - Check if `google-service-account.json` exists
   - Verify the file path in `config.py`
   - Ensure the service account has required permissions

2. **"Permission denied"**
   - Verify the service account has the correct roles
   - Check if APIs are enabled in Google Cloud Console

3. **"Project not found"**
   - Update `GOOGLE_CLOUD_PROJECT` in `config.py`
   - Verify the project ID in your service account key

## File Structure

```
credentials/
├── README.md                    # This file
└── google-service-account.json  # Google Cloud credentials (not in git)
```

---

**Note**: This directory is included in `.gitignore` to prevent accidental commits of sensitive files. 