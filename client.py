import asyncio
import logging
import websockets
import pyaudio
import base64
import json
import time
import os
import wave
import traceback
from websockets.exceptions import ConnectionClosed

# Audio configuration
FORMAT = pyaudio.paInt16
CHANNELS = 1
SEND_SAMPLE_RATE = 8000
RECEIVE_SAMPLE_RATE = 8000
CHUNK_SIZE = 320  # Match dialer.py chunk size

# Debug configuration
AUDIO_DIR = "/tmp/bot_audio_client"
LOG_DIR = "/tmp"

# Setup logging
if not os.path.exists(LOG_DIR):
    os.makedirs(LOG_DIR)

log_filename = f"{LOG_DIR}/client.log"
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_filename),
        logging.StreamHandler()
    ]
)

class AudioClient:
    def __init__(self):
        self.audio_in_queue = asyncio.Queue()
        self.audio_out_queue = asyncio.Queue()
        self.pya = pyaudio.PyAudio()
        self.session_end_flag = False
        self.session_active = [True]  # Use list for mutable reference
        self.sent_audio_chunks = []  # For debugging
        self.received_audio_chunks = []  # For debugging
        self.call_unique_id = f"client_test_{int(time.time() * 1000)}"
        
        # Create audio directory for debugging
        if not os.path.exists(AUDIO_DIR):
            os.makedirs(AUDIO_DIR)

    def save_audio_to_wav(self, audio_chunk, wav_filename):
        """Save audio chunk to WAV file for debugging"""
        try:
            os.makedirs(os.path.dirname(wav_filename), exist_ok=True)
            with wave.open(wav_filename, 'wb') as wav_file:
                wav_file.setnchannels(1)
                wav_file.setsampwidth(2)
                wav_file.setframerate(8000)
                wav_file.writeframes(audio_chunk)
            return True
        except Exception as e:
            logging.error(f"Error saving audio: {e}")
            return False

    def save_complete_audio_debug(self, audio_chunks, call_unique_id, direction):
        """Save complete audio stream for debugging"""
        try:
            if not audio_chunks:
                logging.info(f"No {direction} audio chunks to save")
                return None

            # Create debug directory
            debug_dir = f"{AUDIO_DIR}/{call_unique_id}/debug"
            os.makedirs(debug_dir, exist_ok=True)

            # Combine all audio chunks
            combined_audio = b''.join(audio_chunks)

            # Save as WAV file
            debug_filename = f"{debug_dir}/{call_unique_id}_{direction}_complete.wav"

            with wave.open(debug_filename, 'wb') as wav_file:
                wav_file.setnchannels(1)  # Mono
                wav_file.setsampwidth(2)  # 16-bit
                wav_file.setframerate(8000)  # 8kHz
                wav_file.writeframes(combined_audio)

            # Log statistics
            duration_seconds = len(combined_audio) / (8000 * 2)
            total_chunks = len(audio_chunks)
            total_bytes = len(combined_audio)

            logging.info(f"=== {direction.upper()} AUDIO DEBUG INFO ===")
            logging.info(f"Complete {direction} audio saved to: {debug_filename}")
            logging.info(f"Total chunks {direction}: {total_chunks}")
            logging.info(f"Total bytes {direction}: {total_bytes}")
            logging.info(f"Duration: {duration_seconds:.2f} seconds")
            logging.info(f"Average chunk size: {total_bytes / total_chunks if total_chunks > 0 else 0:.2f} bytes")
            logging.info(f"================================" + "=" * len(direction))

            return debug_filename

        except Exception as e:
            logging.error(f"Error saving complete {direction} audio: {e}")
            logging.error(traceback.format_exc())
            return None

    async def send_start_event(self, websocket, call_unique_id, calleridname="TestUser", accountcode="test_flow", voicename="test_voice"):
        """Send start event to server"""
        logging.info("Sending start event.")
        start_event = {
            "type": "start",
            "sequence_number": 1,
            "stream_id": call_unique_id,
            "bytes": None,
            "start": {
                "stream_sid": call_unique_id,
                "user_name": calleridname,
                "flow_name": accountcode,
                "call_sid": call_unique_id,
                "account_sid": call_unique_id,
                "voice_name": voicename
            }
        }
        await websocket.send(json.dumps(start_event))
        logging.info("Sent start event.")

    async def send_audio(self, websocket, call_unique_id, start_time):
        """Read audio from mic and send to server - matching dialer.py functionality"""
        logging.info(f"Begin sending audio stream for callID: {call_unique_id}")
        sequence_id = 1
        chunk_number = 1
        retry_count = 0
        max_retries = 50
        chunk_size = 320  # Match dialer.py

        try:
            mic_info = self.pya.get_default_input_device_info()
            stream = await asyncio.to_thread(
                self.pya.open,
                format=FORMAT,
                channels=CHANNELS,
                rate=SEND_SAMPLE_RATE,
                input=True,
                input_device_index=mic_info["index"],
                frames_per_buffer=chunk_size,
            )

            while self.session_active[0]:
                try:
                    audio_chunk = await asyncio.to_thread(stream.read, chunk_size, exception_on_overflow=False)

                    if not audio_chunk:
                        logging.info("NOT A VALID AUDIO")
                        retry_count += 1
                        if retry_count >= max_retries:
                            logging.warning("No audio after %d retries", max_retries)
                            break
                        await asyncio.sleep(0.1)
                        continue

                    if not self.session_active[0]:
                        logging.info("SESSION NOT ACTIVE")
                        break

                    # Store the raw audio chunk for debugging
                    self.sent_audio_chunks.append(audio_chunk)

                    encoded_audio = base64.b64encode(audio_chunk).decode("utf-8")
                    elapsed_time_ms = int((time.time() - start_time) * 1000)

                    message_payload = {
                        'type': 'audio',
                        'data': encoded_audio
                    }
                    await websocket.send(json.dumps(message_payload))
                    sequence_id += 1
                    chunk_number += 1
                    retry_count = 0  # Reset on successful send

                    # Log progress every 100 chunks
                    if chunk_number % 500 == 0:
                        logging.info(
                            f"Sent {chunk_number} audio chunks, total bytes: {len(b''.join(self.sent_audio_chunks))}")

                    await asyncio.sleep(0.01)  # Match dialer.py timing

                except asyncio.CancelledError:
                    logging.info("Send audio task cancelled")
                    break
                except Exception as e:
                    logging.error("Exception in send_audio loop: %s", e)
                    if not self.session_active[0]:
                        break
                    await asyncio.sleep(0.1)

        except Exception as e:
            logging.error("Exception in send_audio: %s", e)
            logging.error(traceback.format_exc())
        finally:
            stream.close()
            logging.info(f"Send audio stream ended. Total chunks collected: {len(self.sent_audio_chunks)}")

    async def receive_audio(self, websocket, call_unique_id):
        """Receive messages from server (audio or signals) and play them directly"""
        logging.info("Receiving audio from bot.")
        
        # Initialize audio output stream for direct playback
        output_stream = None
        try:
            output_stream = await asyncio.to_thread(
                self.pya.open,
                format=FORMAT,
                channels=CHANNELS,
                rate=RECEIVE_SAMPLE_RATE,
                output=True,
            )
            logging.info("✅ Audio output stream initialized")
        except Exception as e:
            logging.error(f"❌ Failed to initialize audio output: {e}")
            return

        try:
            while self.session_active[0]:
                try:
                    # Use timeout to allow periodic session checking
                    data = await asyncio.wait_for(websocket.recv(), timeout=5)

                    try:
                        parsed_message = json.loads(data)
                        message_type = parsed_message.get('type')
                        message_data = parsed_message.get('data')
                        logging.info(f'DATA_RECEIVED: {message_type}')

                        if not self.session_active[0]:
                            break

                        if message_type == 'audio':
                            audio_bytes = base64.b64decode(message_data)

                            # Store the raw received audio chunk for debugging
                            self.received_audio_chunks.append(audio_bytes)

                            # Play audio directly without saving to file
                            try:
                                logging.info(f"🔊 Playing audio directly ({len(audio_bytes)} bytes)")
                                
                                # Play audio in chunks
                                chunk_size = 1024
                                for i in range(0, len(audio_bytes), chunk_size):
                                    if not self.session_active[0]:
                                        break
                                    chunk = audio_bytes[i:i+chunk_size]
                                    if chunk:
                                        await asyncio.to_thread(output_stream.write, chunk)
                                
                                logging.info(f"✅ Finished playing audio")
                                
                            except Exception as e:
                                logging.error(f"Error playing audio: {e}")

                            # Log progress every 50 received chunks
                            if len(self.received_audio_chunks) % 50 == 0:
                                logging.debug(
                                    f"Received {len(self.received_audio_chunks)} audio chunks, total bytes: {len(b''.join(self.received_audio_chunks))}")

                        elif message_type == 'signal' and message_data == '__SESSION_END__':
                            logging.info("Session end signal received")
                            logging.info(parsed_message.get('details', 'No details'))
                            self.session_active[0] = False
                            break

                        elif message_type == 'signal' and message_data == '__CONNECT_AGENT__':
                            logging.info("Connect agent signal received - customer interested")
                            logging.info(parsed_message.get('details', 'No details'))
                            # TODO: Implement agent connection logic here
                            logging.info("Agent connection would be initiated here")

                        elif message_type == 'signal' and message_data == '__INTERRUPT__':
                            logging.info("Interrupt signal received")
                            # For interrupt, we could stop current playback if needed

                    except json.JSONDecodeError as e:
                        logging.error("Error decoding JSON: %s", e)
                        continue

                except asyncio.TimeoutError:
                    # Timeout is expected for periodic checks
                    continue
                except ConnectionClosed:
                    logging.info("WebSocket connection closed")
                    self.session_active[0] = False
                    break
                except Exception as e:
                    logging.error("Exception in receive_audio: %s", e)
                    if not self.session_active[0]:
                        break
                    continue

        except Exception as e:
            logging.error("Fatal exception in receive_audio: %s", e)
            logging.error(traceback.format_exc())
        finally:
            if output_stream:
                try:
                    output_stream.close()
                    logging.info("Audio output stream closed")
                except:
                    pass
            logging.info(f"Receive audio stream ended. Total chunks collected: {len(self.received_audio_chunks)}")

    async def manage_websocket_connection(self, websocket_uri, call_unique_id, calleridname="TestUser", accountcode="test_flow", voicename="test_voice"):
        """Manage WebSocket connection - matching dialer.py functionality"""
        try:
            async with websockets.connect(
                    websocket_uri,
                    ping_interval=20,
                    ping_timeout=10,
                    close_timeout=10
            ) as ws:
                await self.send_start_event(ws, call_unique_id, calleridname, accountcode, voicename)
                start_time = time.time()

                # Create tasks with proper error handling
                send_task = asyncio.create_task(
                    self.send_audio(ws, call_unique_id, start_time)
                )
                receive_task = asyncio.create_task(
                    self.receive_audio(ws, call_unique_id)
                )

                # Wait for either task to complete or session to end
                done, pending = await asyncio.wait(
                    [send_task, receive_task],
                    return_when=asyncio.FIRST_COMPLETED
                )

                # Cancel pending tasks
                for task in pending:
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass

                # Check for exceptions in completed tasks
                for task in done:
                    try:
                        await task
                    except Exception as e:
                        logging.error(f"Task completed with exception: {e}")

        except ConnectionClosed:
            logging.info("WebSocket connection closed")
        except Exception as e:
            logging.error(f"Exception in manage_websocket_connection: {e}")
            logging.error(traceback.format_exc())
        finally:
            self.session_active[0] = False

            # Save the complete audio streams for debugging
            logging.info("=== SAVING DEBUG AUDIO FILES ===")

            sent_debug_file = None
            received_debug_file = None

            if self.sent_audio_chunks:
                sent_debug_file = self.save_complete_audio_debug(self.sent_audio_chunks, call_unique_id, "sent")
                if sent_debug_file:
                    logging.info(f"Sent audio debug file created: {sent_debug_file}")
            else:
                logging.info("No sent audio chunks to save for debugging")

            if self.received_audio_chunks:
                received_debug_file = self.save_complete_audio_debug(self.received_audio_chunks, call_unique_id, "received")
                if received_debug_file:
                    logging.info(f"Received audio debug file created: {received_debug_file}")
            else:
                logging.info("No received audio chunks to save for debugging")

            # Summary log
            logging.info("=== DEBUG FILES SUMMARY ===")
            logging.info(f"Call ID: {call_unique_id}")
            logging.info(f"Sent audio file: {sent_debug_file if sent_debug_file else 'None'}")
            logging.info(f"Received audio file: {received_debug_file if received_debug_file else 'None'}")
            logging.info(f"Debug directory: {AUDIO_DIR}/{call_unique_id}/debug/")
            logging.info("===========================")

            logging.info("WebSocket connection management ended")

    async def run(self, websocket_uri="ws://0.0.0.0:8766/ws", calleridname="TestUser", accountcode="test_flow", voicename="test_voice"):
        """Run the client - matching dialer.py functionality"""
        try:
            logging.info("Client.py script started.")
            logging.info(f"Connecting to: {websocket_uri}")
            logging.info(f"Call ID: {self.call_unique_id}")
            logging.info(f"User: {calleridname}, Flow: {accountcode}, Voice: {voicename}")

            # Run the main async function
            await self.manage_websocket_connection(
                websocket_uri, self.call_unique_id, calleridname, accountcode, voicename
            )

        except KeyboardInterrupt:
            logging.info("Script interrupted by user")
        except Exception as e:
            logging.error(f"Fatal error in main: {e}")
            logging.error(traceback.format_exc())
        finally:
            self.pya.terminate()
            logging.info("Client.py script ended.")

if __name__ == "__main__":
    client = AudioClient()
    
    # Configuration for local testing
    websocket_uri = "ws://0.0.0.0:8766/ws"  # Default local server
    calleridname = "TestUser"
    accountcode = "test_flow"
    voicename = "test_voice"
    
    # You can modify these parameters for testing
    # websocket_uri = "ws://**********:8766/ws"  # For testing with remote server
    
    asyncio.run(client.run(
        websocket_uri=websocket_uri,
        calleridname=calleridname,
        accountcode=accountcode,
        voicename=voicename
    ))