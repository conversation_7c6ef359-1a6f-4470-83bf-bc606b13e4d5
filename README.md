# PolicyBazaar Voice Bot - Production Dialer Integration

## 🎯 Overview

Professional voice bot system for PolicyBazaar outbound calls with built-in speech recognition and professional conversation flow management.

## ✨ Features

- **Built-in Speech Recognition**: Direct Google Cloud Speech-to-Text integration
- **Professional Call Flow**: Handles interruptions and natural conversation patterns
- **Dialer Integration**: WebSocket-based integration with dialer systems
- **Production Ready**: Robust error handling and logging
- **Self-contained**: All functionality in single file for easy deployment
- **Modern Package Management**: Uses `uv` for fast, reliable dependency management

## 🏗️ Architecture

```
voice-bot1/
├── voice_bot.py                    # Main voice bot application (self-contained)
├── config.py                       # Configuration settings
├── dialer.py                       # Dialer integration utilities
├── matrixteam.json                 # Google Cloud credentials
├── requirements.txt                # Python dependencies (cleaned)
├── pyproject.toml                  # Modern project configuration
├── start.sh                        # Production startup script
├── .gitignore                      # Git ignore rules
├── README.md                       # This documentation
├── credentials/                    # Google Cloud setup guide
│   ├── google-service-account.json
│   └── README.md
└── logs/                           # Application logs (actively used)
    └── voice_bot.log              # Log file (actively used)
```

## 🚀 Quick Start

### 1. Prerequisites

- Python 3.8.1+
- Google Cloud credentials (`matrixteam.json`)
- Required APIs enabled:
  - Speech-to-Text API
  - Text-to-Speech API
  - Vertex AI API

### 2. Installation

#### Option A: Using `uv` (Recommended - Modern & Fast)

```bash
# Install uv (modern package manager)
curl -LsSf https://astral.sh/uv/install.sh | sh

# Add to PATH
export PATH="$HOME/.local/bin:$PATH"

# Create virtual environment and install dependencies
uv venv
source .venv/bin/activate
uv pip install -r requirements.txt
```

#### Option B: Using pip (Traditional)

```bash
# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
```

### 3. Configuration

Update `config.py` with your settings:
- Google Cloud project ID
- Service account file path
- Server configuration
- Speech recognition parameters

### 4. Start Production Server

#### Using the startup script (Recommended)

```bash
# Make script executable (first time only)
chmod +x start.sh

# Start the voice bot
./start.sh
```

#### Manual startup

```bash
# Set credentials
export GOOGLE_APPLICATION_CREDENTIALS="matrixteam.json"

# Start the voice bot
python voice_bot.py
```

## 🔌 Dialer Integration

The voice bot exposes a WebSocket server for dialer integration:

- **Endpoint**: `ws://host:8766/ws`
- **Protocol**: JSON messages over WebSocket
- **Audio Format**: Base64-encoded audio data
- **Response**: Audio responses with transcript metadata

### Message Format

```json
{
  "type": "audio",
  "data": "base64_encoded_audio",
  "session_id": "unique_session_id"
}
```

## 🎤 Speech Recognition

### Built-in Implementation

- **Direct Integration**: Uses Google Cloud Speech-to-Text directly
- **Audio Processing**: Converts dialer audio (8kHz) to processing format (16kHz)
- **Intent Analysis**: Simple keyword-based intent detection
- **Response Generation**: Hardcoded responses for Phase 1 implementation

### Configuration

```python
SPEECH_CONFIG = {
    "language_code": "en-IN",
    "model": "latest_long",
    "use_enhanced": True,
    "enable_automatic_punctuation": True
}
```

## 🌐 Language Support

- **Fixed Language**: Currently configured for English (Indian)
- **Extensible**: Easy to add more languages in config
- **TTS Support**: Text-to-speech in configured language

## 📊 Monitoring & Logging

- **Real-time Logs**: Console and file logging
- **Session Tracking**: Active connections and performance metrics
- **Error Handling**: Comprehensive error logging and recovery
- **Performance Metrics**: Response times and success rates

## 🚀 Production Deployment

### System Requirements

- **CPU**: 2+ cores recommended
- **Memory**: 4GB+ RAM
- **Storage**: 10GB+ for logs and temporary files
- **Network**: Stable internet connection for Google Cloud APIs

### Environment Variables

```bash
export GOOGLE_APPLICATION_CREDENTIALS="matrixteam.json"
export PYTHONPATH="${PYTHONPATH}:$(pwd)"
```

### Package Management

#### Using `uv` (Recommended)

```bash
# Install dependencies
uv sync

# Add new dependency
uv add package-name

# Update dependencies
uv lock --upgrade
```

#### Using pip

```bash
# Install dependencies
pip install -r requirements.txt

# Update requirements
pip freeze > requirements.txt
```

## 🔧 Configuration

### Speech Recognition

```python
SPEECH_CONFIG = {
    "language_code": "en-IN",
    "model": "latest_long",
    "use_enhanced": True,
    "enable_automatic_punctuation": True
}
```

### Server Settings

```python
SERVER_CONFIG = {
    "websocket": {
        "host": "0.0.0.0",
        "port": 8766,
        "ping_interval": 20,
        "ping_timeout": 20
    }
}
```

## 📝 API Reference

### VoiceBot Class

```python
class PolicyBazaarVoiceBot:
    async def start_server()           # Start WebSocket server
    async def process_dialer_audio()   # Process dialer audio
    async def synthesize_speech()      # Convert text to speech
    def get_server_status()            # Get server metrics
```

## 🐛 Troubleshooting

### Common Issues

1. **Google Cloud Authentication**
   - Verify `matrixteam.json` exists and has correct permissions
   - Check API enablement in Google Cloud Console

2. **Audio Processing**
   - Ensure audio format is 8kHz/16-bit PCM for dialer
   - Check pydub installation for audio conversion

3. **WebSocket Connections**
   - Verify firewall settings for port 8766
   - Check dialer connection parameters

4. **Package Management**
   - Use `uv` for faster, more reliable dependency installation
   - Clear cache: `uv cache clean`

### Logs

Check logs in:
- Console output
- `logs/voice_bot.log` (if configured)

## 🔌 Integration with Dialer

The voice bot is designed to work with:
- **WebSocket Protocol**: Real-time bidirectional communication
- **Audio Streaming**: Continuous audio processing
- **Session Management**: Unique session tracking per call
- **Error Recovery**: Graceful handling of connection issues

## 📄 License

Internal use for PolicyBazaar. All rights reserved.
