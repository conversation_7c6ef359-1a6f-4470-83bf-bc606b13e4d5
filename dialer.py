#!/root/.pyenv/shims/python3

import os
import base64
import json
import shutil
import wave
import time
import sys
import asyncio
import logging
import traceback
import websockets
#from pydub import AudioSegment
from websockets.exceptions import ConnectionClosed

LOG_DIR = "/tmp"
AUDIO_DIR = "/tmp/bot_audio"

if not os.path.exists(LOG_DIR):
    os.makedirs(LOG_DIR)

log_filename = f"{LOG_DIR}/bot.log"
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_filename),
        logging.StreamHandler(sys.stderr)
    ]
)

AUDIO_FD = 3


class SessionEndedException(Exception):
    """Custom exception to signal session end without using sys.exit()"""
    pass


def read_agi_env():
    logging.info("Reading AGI environment from stdin.")
    env = {}
    try:
        for line in sys.stdin:
            line = line.strip()
            if not line:
                break
            key, value = line.split(":", 1)
            env[key.strip().lower()] = value.strip()
    except Exception as e:
        logging.error(f"Error reading AGI env: {e}")
    logging.info("AGI environment: %s", env)
    return env


def save_audio_to_wav(audio_chunk, wav_filename):
    try:
        #logging.info("Saving audio to: %s", wav_filename)
        os.makedirs(os.path.dirname(wav_filename), exist_ok=True)
        with wave.open(wav_filename, 'wb') as wav_file:
            wav_file.setnchannels(1)
            wav_file.setsampwidth(2)
            wav_file.setframerate(8000)
            wav_file.writeframes(audio_chunk)

        #audio = AudioSegment.from_file(wav_filename)

        # Get basic stats
        #logging.info(f"Channels: {audio.channels}")
        #logging.info(f"Sample rate: {audio.frame_rate} Hz")
        #logging.info(f"Frame width: {audio.sample_width} bytes")
        #logging.info(f"Duration: {len(audio)} ms")
        #logging.info(f"Total frames: {audio.frame_count()}")
        #logging.info(f"RMS Volume: {audio.rms}")
        #logging.info(f"dBFS (average volume): {audio.dBFS:.2f} dBFS")
        return True
    except Exception as e:
        logging.error(f"Error saving audio: {e}")
        return False


def save_complete_audio_debug(audio_chunks, call_unique_id, direction):
    """Save complete audio stream (sent or received) into a single WAV file for debugging

    Args:
        audio_chunks: List of raw audio byte chunks
        call_unique_id: Unique identifier for the call
        direction: 'sent' or 'received' to indicate audio direction
    """
    try:
        if not audio_chunks:
            logging.info(f"No {direction} audio chunks to save")
            return None

        # Create debug directory
        debug_dir = f"{AUDIO_DIR}/{call_unique_id}/debug"
        os.makedirs(debug_dir, exist_ok=True)

        # Combine all audio chunks
        combined_audio = b''.join(audio_chunks)

        # Save as WAV file
        debug_filename = f"{debug_dir}/{call_unique_id}_{direction}_complete.wav"

        with wave.open(debug_filename, 'wb') as wav_file:
            wav_file.setnchannels(1)  # Mono
            wav_file.setsampwidth(2)  # 16-bit
            wav_file.setframerate(8000)  # 8kHz
            wav_file.writeframes(combined_audio)

        # Log statistics
        duration_seconds = len(combined_audio) / (8000 * 2)  # samples / (sample_rate * bytes_per_sample)
        total_chunks = len(audio_chunks)
        total_bytes = len(combined_audio)

        logging.info(f"=== {direction.upper()} AUDIO DEBUG INFO ===")
        logging.info(f"Complete {direction} audio saved to: {debug_filename}")
        logging.info(f"Total chunks {direction}: {total_chunks}")
        logging.info(f"Total bytes {direction}: {total_bytes}")
        logging.info(f"Duration: {duration_seconds:.2f} seconds")
        logging.info(f"Average chunk size: {total_bytes / total_chunks if total_chunks > 0 else 0:.2f} bytes")
        logging.info(f"================================" + "=" * len(direction))

        return debug_filename

    except Exception as e:
        logging.error(f"Error saving complete {direction} audio: {e}")
        logging.error(traceback.format_exc())
        return None


def open_audio_stream(fd=3):
    logging.info("Opening audio stream from FD %d", fd)
    return os.fdopen(fd, 'rb')


async def send_start_event(ws, call_unique_id, calleridname, accountcode, voicename):
    logging.info("Sending start event.")
    start_event = {
        "type": "start",
        "sequence_number": 1,
        "stream_id": call_unique_id,
        "bytes": None,
        "start": {
            "stream_sid": call_unique_id,
            "user_name": calleridname,
            "flow_name": accountcode,
            "call_sid": call_unique_id,
            "account_sid": call_unique_id,
            "voice_name": voicename
        }
    }
    await ws.send(json.dumps(start_event))
    logging.info("Sent start event.")


async def send_audio(ws, call_unique_id, start_time, session_active, sent_audio_chunks):
    logging.info(f"Begin sending audio stream for callID: {call_unique_id}")
    sequence_id = 1
    chunk_number = 1
    retry_count = 0
    max_retries = 50
    chunk_size = 320  # Return to original chunk size

    try:
        with open_audio_stream(AUDIO_FD) as audio_fd:
            while session_active[0]:  # Use list for mutable reference
                try:
                    audio_chunk = audio_fd.read(chunk_size)

                    if not audio_chunk:
                        logging.info("NOT A VALID AUDIO")
                        retry_count += 1
                        if retry_count >= max_retries:
                            logging.warning("No audio after %d retries", max_retries)
                            break
                        await asyncio.sleep(0.1)
                        continue

                    if not session_active[0]:  # Check again after sleep
                        logging.info("SESSION NOT ACTIVATE")
                        break

                    # Store the raw audio chunk for debugging
                    sent_audio_chunks.append(audio_chunk)

                    # encoded_audio = base64.b64encode(audio_chunk).decode('utf-8')
                    encoded_audio = base64.b64encode(audio_chunk).decode("utf-8")
                    elapsed_time_ms = int((time.time() - start_time) * 1000)

                    message_payload = {
                        'type': 'audio',
                        'data': encoded_audio
                    }
                    # logging.info(f'message_payload: {message_payload}')
                    await ws.send(json.dumps(message_payload))
                    sequence_id += 1
                    chunk_number += 1
                    retry_count = 0  # Reset on successful send

                    # Log progress every 100 chunks
                    if chunk_number % 500 == 0:
                        logging.info(
                            f"Sent {chunk_number} audio chunks, total bytes: {len(b''.join(sent_audio_chunks))}")

                    await asyncio.sleep(0.01)  # Original timing

                except asyncio.CancelledError:
                    logging.info("Send audio task cancelled")
                    break
                except Exception as e:
                    logging.error("Exception in send_audio loop: %s", e)
                    if not session_active[0]:
                        break
                    await asyncio.sleep(0.1)

    except Exception as e:
        logging.error("Exception in send_audio: %s", e)
        logging.error(traceback.format_exc())
    finally:
        logging.info(f"Send audio stream ended. Total chunks collected: {len(sent_audio_chunks)}")


async def receive_audio(ws, call_unique_id, session_active, received_audio_chunks):
    logging.info("Receiving audio from bot.")
    audio_queue = asyncio.Queue()  # Return to original queue without size limit

    async def process_queue():
        logging.info("process_queue... started.")
        current_playback_task = None

        try:
            while session_active[0]:
                try:
                    # Use timeout to allow periodic checking of session_active
                    queue_item = await asyncio.wait_for(audio_queue.get(), timeout=5)

                    if not session_active[0]:
                        break

                    # Handle different queue item types
                    if isinstance(queue_item, tuple) and len(queue_item) == 2:
                        temp_wav_file, audio_length = queue_item
                        is_streaming_chunk = False
                    elif isinstance(queue_item, dict) and queue_item.get('type') == 'streaming_chunk':
                        temp_wav_file = queue_item['file']
                        audio_length = queue_item['length']
                        is_streaming_chunk = True
                        chunk_info = queue_item.get('chunk_info', {})
                    else:
                        logging.warning(f"Unknown queue item format: {queue_item}")
                        continue

                    if os.path.getsize(temp_wav_file) <= 44:
                        logging.info("FILE SIZE ISSUE")
                    elif os.path.exists(temp_wav_file):
                        base_filename = temp_wav_file.replace(".wav", "")

                        # For streaming chunks, use immediate playback without STREAM FILE
                        if is_streaming_chunk:
                            # Use direct audio output for streaming chunks (real-time playback)
                            try:
                                # Read the audio data and write directly to audio output
                                with open(temp_wav_file, 'rb') as f:
                                    # Skip WAV header (44 bytes)
                                    f.seek(44)
                                    audio_data = f.read()

                                # Write raw audio data to file descriptor 3 (audio output)
                                os.write(3, audio_data)
                                logging.debug(f"Streamed chunk: {chunk_info.get('chunk_num', '?')}/{chunk_info.get('total_chunks', '?')}")

                                # Small delay for real-time playback (20ms per chunk)
                                await asyncio.sleep(0.02)

                            except Exception as e:
                                logging.error(f"Error streaming audio chunk: {e}")
                                # Fallback to STREAM FILE for this chunk
                                sys.stdout.write(f'STREAM FILE {base_filename} ""\n')
                                sys.stdout.flush()
                                await asyncio.sleep(0.02)
                        else:
                            # Traditional complete file playback
                            sys.stdout.write(f'STREAM FILE {base_filename} ""\n')
                            sys.stdout.flush()
                            logging.info(f"Playing audio: {base_filename}")

                            # Calculate sleep time based on audio length
                            sleep_time = max(audio_length / 16000.0, 0.02)
                            await asyncio.sleep(sleep_time)
                    else:
                        logging.info("No valid audio content to play, continuing.")

                except asyncio.TimeoutError:
                    # Check if session is still active before sending timeout signal
                    if session_active[0]:
                        logging.info('[CLIENT] No audio from client for 5 secs. Sending timeout signal to client')
                        try:
                            message_payload = {'type': 'signal', 'data': '__TIMEOUT__'}
                            await ws.send(json.dumps(message_payload))
                        except Exception as e:
                            logging.error(f"Failed to send timeout signal: {e}")
                            break
                    continue
                except asyncio.CancelledError:
                    # Handle task cancellation gracefully
                    logging.info("Process queue task was cancelled")
                    break
                except Exception as e:
                    logging.error(f"Error in process_queue: {e}")
                    if not session_active[0]:
                        break

        except asyncio.CancelledError:
            # Handle cancellation at the outer level
            logging.info("Process queue cancelled during shutdown")
        except Exception as e:
            logging.error(f"Fatal error in process_queue: {e}")
        finally:
            logging.info("Process queue ended")

    # Start the queue processor task
    queue_task = asyncio.create_task(process_queue())

    try:
        while session_active[0]:
            try:
                # Use timeout to allow periodic session checking
                data = await asyncio.wait_for(ws.recv(), timeout=5)

                try:
                    parsed_message = json.loads(data)
                    message_type = parsed_message.get('type')
                    message_data = parsed_message.get('data')
                    logging.info(f'DATA_RECEIVED: {message_type}')

                    if not session_active[0]:
                        break

                    if message_type == 'audio':
                        audio_bytes = base64.b64decode(message_data)

                        # Store the raw received audio chunk for debugging
                        received_audio_chunks.append(audio_bytes)

                        # Check if this is a streaming chunk
                        chunk_info = parsed_message.get('chunk_info')
                        if chunk_info:
                            # Handle streaming audio chunks for real-time interruption
                            chunk_num = chunk_info.get('chunk_num', 1)
                            total_chunks = chunk_info.get('total_chunks', 1)
                            is_final = chunk_info.get('is_final', True)
                            is_streaming = chunk_info.get('streaming', False)

                            logging.info(f"📦 Received streaming audio chunk {chunk_num}/{total_chunks} ({len(audio_bytes)} bytes)")

                            # For streaming, create timestamped filename with chunk info
                            timestamp = int(time.time() * 1000)
                            filename = f"{AUDIO_DIR}/{call_unique_id}/{call_unique_id}_recv_{timestamp}_chunk_{chunk_num}.wav"

                            if save_audio_to_wav(audio_bytes, filename):
                                # Queue streaming chunk with special handling
                                streaming_item = {
                                    'type': 'streaming_chunk',
                                    'file': filename,
                                    'length': len(audio_bytes),
                                    'chunk_info': chunk_info
                                }
                                await audio_queue.put(streaming_item)
                                logging.debug(f"Queued streaming chunk: {filename}")

                            if is_final:
                                logging.info(f"✅ Completed streaming audio reception: {total_chunks} chunks")
                        else:
                            # Handle complete audio file (legacy mode)
                            timestamp = int(time.time() * 1000)
                            filename = f"{AUDIO_DIR}/{call_unique_id}/{call_unique_id}_recv_{timestamp}.wav"

                            if save_audio_to_wav(audio_bytes, filename):
                                await audio_queue.put((filename, len(audio_bytes)))
                                logging.debug(f"Queued audio: {filename}, queue size: {audio_queue.qsize()}")

                        # Log progress every 50 received chunks
                        if len(received_audio_chunks) % 50 == 0:
                            logging.debug(
                                f"Received {len(received_audio_chunks)} audio chunks, total bytes: {len(b''.join(received_audio_chunks))}")

                    elif message_type == 'signal' and message_data == '__SESSION_END__':
                        logging.info("Session end signal received")
                        logging.info(parsed_message.get('details', 'No details'))
                        session_active[0] = False
                        break

                    elif message_type == 'signal' and message_data == '__CONNECT_AGENT__':
                        logging.info("Connect agent signal received - customer interested")
                        logging.info(parsed_message.get('details', 'No details'))
                        # TODO: Implement agent connection logic here
                        # For now, just log and continue the session
                        logging.info("Agent connection would be initiated here")

                    elif message_type == 'signal' and message_data == '__INTERRUPT__':
                        logging.info("Interrupt signal received, clearing audio queue")
                        # Clear the queue
                        while not audio_queue.empty():
                            try:
                                audio_queue.get_nowait()
                            except asyncio.QueueEmpty:
                                break

                except json.JSONDecodeError as e:
                    logging.error("Error decoding JSON: %s", e)
                    continue

            except asyncio.TimeoutError:
                # Timeout is expected for periodic checks
                continue
            except ConnectionClosed:
                logging.info("WebSocket connection closed")
                session_active[0] = False
                break
            except Exception as e:
                logging.error("Exception in receive_audio: %s", e)
                if not session_active[0]:
                    break
                continue

    except Exception as e:
        logging.error("Fatal exception in receive_audio: %s", e)
        logging.error(traceback.format_exc())
    finally:
        # Cancel the queue processor and cleanup
        queue_task.cancel()
        try:
            await queue_task
        except asyncio.CancelledError:
            pass

        logging.info(f"Receive audio stream ended. Total chunks collected: {len(received_audio_chunks)}")


async def manage_websocket_connection(websocket_uri, call_unique_id, calleridname, accountcode, voicename):
    session_active = [True]  # Use list for mutable reference across tasks
    sent_audio_chunks = []  # List to collect all sent audio chunks
    received_audio_chunks = []  # List to collect all received audio chunks

    try:
        async with websockets.connect(
                websocket_uri,
                ping_interval=20,
                ping_timeout=10,
                close_timeout=10
        ) as ws:
            await send_start_event(ws, call_unique_id, calleridname, accountcode, voicename)
            start_time = time.time()

            # Create tasks with proper error handling
            send_task = asyncio.create_task(
                send_audio(ws, call_unique_id, start_time, session_active, sent_audio_chunks)
            )
            receive_task = asyncio.create_task(
                receive_audio(ws, call_unique_id, session_active, received_audio_chunks)
            )

            # Wait for either task to complete or session to end
            done, pending = await asyncio.wait(
                [send_task, receive_task],
                return_when=asyncio.FIRST_COMPLETED
            )

            # Cancel pending tasks
            for task in pending:
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass

            # Check for exceptions in completed tasks
            for task in done:
                try:
                    await task
                except Exception as e:
                    logging.error(f"Task completed with exception: {e}")

    except ConnectionClosed:
        logging.info("WebSocket connection closed")
    except Exception as e:
        logging.error(f"Exception in manage_websocket_connection: {e}")
        logging.error(traceback.format_exc())
    finally:
        session_active[0] = False

        # Save the complete audio streams for debugging
        logging.info("=== SAVING DEBUG AUDIO FILES ===")

        sent_debug_file = None
        received_debug_file = None

        if sent_audio_chunks:
            sent_debug_file = save_complete_audio_debug(sent_audio_chunks, call_unique_id, "sent")
            if sent_debug_file:
                logging.info(f"Sent audio debug file created: {sent_debug_file}")
        else:
            logging.info("No sent audio chunks to save for debugging")

        if received_audio_chunks:
            received_debug_file = save_complete_audio_debug(received_audio_chunks, call_unique_id, "received")
            if received_debug_file:
                logging.info(f"Received audio debug file created: {received_debug_file}")
        else:
            logging.info("No received audio chunks to save for debugging")

        # Summary log
        logging.info("=== DEBUG FILES SUMMARY ===")
        logging.info(f"Call ID: {call_unique_id}")
        logging.info(f"Sent audio file: {sent_debug_file if sent_debug_file else 'None'}")
        logging.info(f"Received audio file: {received_debug_file if received_debug_file else 'None'}")
        logging.info(f"Debug directory: {AUDIO_DIR}/{call_unique_id}/debug/")
        logging.info("===========================")

        logging.info("WebSocket connection management ended")


if __name__ == "__main__":
    logging.info("Bot.py script started.")

    try:
        agi_env = read_agi_env()
        call_unique_id = agi_env.get('agi_uniqueid', 'unknown')
        calleridname = agi_env.get('agi_calleridname', 'unknown')
        accountcode = agi_env.get('agi_accountcode', 'unknown')
        voicename = agi_env.get('agi_dnid', 'unknown')

        websocket_uri = "ws://**********:8766/ws"
        #websocket_uri = "ws://************:8766/ws"

        # Run the main async function
        asyncio.run(manage_websocket_connection(
            websocket_uri, call_unique_id, calleridname, accountcode, voicename
        ))

    except KeyboardInterrupt:
        logging.info("Script interrupted by user")
    except Exception as e:
        logging.error(f"Fatal error in main: {e}")
        logging.error(traceback.format_exc())
    finally:
        logging.info("Bot.py script ended.")
