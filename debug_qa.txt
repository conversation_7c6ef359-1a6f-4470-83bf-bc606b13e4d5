🚀 Starting PolicyBazaar Voice Bot...
✅ Using uv package manager
🔧 Activating virtual environment...
✅ Environment setup complete
🌐 Starting WebSocket server on port 8766...
🔌 Dialer endpoint: ws://0.0.0.0:8766/ws
📋 Press Ctrl+C to stop the server
/opt/bot/pbbot/.venv/lib/python3.10/site-packages/webrtcvad.py:1: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
2025-09-08 15:42:16,451 - __main__ - INFO - 🎤 PolicyBazaar Voice Bot - AI-Powered Outbound Call System
2025-09-08 15:42:16,451 - __main__ - INFO - 🤖 Natural conversations using Vertex AI Gemini
2025-09-08 15:42:16,452 - __main__ - INFO - 🚀 Initializing PolicyBazaar Voice Bot...
2025-09-08 15:42:16,485 - __main__ - INFO - ✅ Google Cloud services initialized
2025-09-08 15:42:16,485 - __main__ - INFO - 🎤 WebRTC VAD initialized: aggressiveness=2, sample_rate=8000Hz, frame_duration=20ms
2025-09-08 15:42:16,485 - __main__ - INFO - 📊 Frame size: 160 samples (320 bytes)
2025-09-08 15:42:16,486 - __main__ - INFO - ✅ PolicyBazaar Voice Bot initialized successfully
2025-09-08 15:42:16,486 - __main__ - INFO - 🔊 VAD Mode: Moderate aggressive (balanced)
2025-09-08 15:42:16,486 - __main__ - INFO - ✅ Voice bot instance created successfully
2025-09-08 15:42:16,486 - __main__ - INFO - 🚀 Starting WebSocket server on 0.0.0.0:8766/ws
2025-09-08 15:42:16,487 - websockets.server - INFO - server listening on 0.0.0.0:8766
2025-09-08 15:42:16,488 - __main__ - INFO - ✅ WebSocket server started successfully
2025-09-08 15:42:16,488 - __main__ - INFO - 🔄 Waiting for dialer connections on /ws endpoint...
2025-09-08 15:43:24,408 - websockets.server - INFO - connection open
2025-09-08 15:43:24,409 - __main__ - INFO - 🔌 New WebSocket connection from dialer: dialer_1757326404409
2025-09-08 15:43:24,409 - __main__ - INFO - 🔓 Session dialer_1757326404409 state set to 'active', bot state set to 'listening'
2025-09-08 15:43:24,409 - __main__ - INFO - ⏳ Waiting for start event from dialer dialer_1757326404409...
2025-09-08 15:43:24,411 - __main__ - INFO - 🚀 START EVENT RECEIVED for dialer_1757326404409
2025-09-08 15:43:24,411 - __main__ - INFO - 👤 Customer: 919464122661, Flow: , Call ID: 1757326400.1927
2025-09-08 15:43:24,411 - __main__ - INFO - 🔊 Converting to speech: 'Hello! I am your PolicyBazaar voice assistant. How can I help you today?'
2025-09-08 15:43:25,678 - __main__ - INFO - ✅ TTS completed (size: 79062 bytes)
2025-09-08 15:43:25,686 - __main__ - INFO - 📤 GREETING SENT: Sent complete greeting audio to dialer dialer_1757326404409
2025-09-08 15:43:25,687 - __main__ - INFO - 🕒 Greeting duration: 4.9s, will reset to listening automatically
2025-09-08 15:43:25,687 - __main__ - INFO - 🕒 Cooldown period started (3s) after greeting for dialer_1757326404409
2025-09-08 15:43:30,020 - __main__ - INFO - 🛑 Customer speech detected while bot speaking - sending interrupt to dialer
2025-09-08 15:43:30,021 - __main__ - INFO - 🛑 Sent __INTERRUPT__ signal to dialer for dialer_1757326404409
2025-09-08 15:43:30,021 - __main__ - INFO - 🎤 Speech detected by VAD, starting collection...
2025-09-08 15:43:30,636 - __main__ - INFO - 🔇 VAD Silence detected: 0.5s
2025-09-08 15:43:31,134 - __main__ - INFO - 🔇 VAD Silence detected: 1.0s
2025-09-08 15:43:31,231 - __main__ - INFO - 🎤 VAD Speech resumed after 1.1s silence
2025-09-08 15:43:31,514 - __main__ - INFO - 🎤 VAD Speech resumed after 0.1s silence
2025-09-08 15:43:31,994 - __main__ - INFO - 🎯 Processing audio buffer: 32000 bytes (2.0s)
2025-09-08 15:43:31,994 - __main__ - INFO - 📊 Speech duration: 2.0s, Silence duration: 0.0s
2025-09-08 15:43:31,995 - __main__ - INFO - 🎤 VAD detected speech: True
2025-09-08 15:43:31,996 - __main__ - INFO - ✅ Converted dialer audio for Gemini: 32000 -> 64042 bytes
2025-09-08 15:43:31,996 - __main__ - INFO - 🎤 Calling Vertex AI Gemini with audio input (size: 64042 bytes)
2025-09-08 15:43:31,996 - __main__ - INFO - 📋 Using conversation history with 1 messages
2025-09-08 15:43:35,088 - __main__ - INFO - ✅ ############## AI Response from Vertex AI (audio input): 'Hello! I can certainly help you with insurance-related queries. What kind of insurance are you interested in today?'
2025-09-08 15:43:35,089 - __main__ - INFO - 🔊 Converting to speech: 'Hello! I can certainly help you with insurance-related queries. What kind of insurance are you interested in today?'
2025-09-08 15:43:35,846 - __main__ - INFO - ✅ TTS completed (size: 111814 bytes)
2025-09-08 15:43:35,856 - __main__ - INFO - 📤 Sent complete TTS audio to dialer dialer_1757326404409
2025-09-08 15:43:35,856 - __main__ - INFO - 🕒 TTS duration: 7.0s, will reset to listening automatically
2025-09-08 15:43:35,856 - __main__ - INFO - 🕒 Cooldown period (3s) started for dialer_1757326404409
2025-09-08 15:43:35,859 - __main__ - INFO - 🛑 Customer speech detected while bot speaking - sending interrupt to dialer
2025-09-08 15:43:35,859 - __main__ - INFO - 🛑 Sent __INTERRUPT__ signal to dialer for dialer_1757326404409
2025-09-08 15:43:35,859 - __main__ - INFO - 🎤 Speech detected by VAD, starting collection...
2025-09-08 15:43:35,860 - __main__ - INFO - 🔇 Cooldown period active (3s after response), buffering speech...
2025-09-08 15:43:35,868 - __main__ - INFO - 📡 Signal received from dialer dialer_1757326404409: __TIMEOUT__
2025-09-08 15:43:36,872 - __main__ - INFO - 🔇 Cooldown period active (3s after response), buffering speech...
2025-09-08 15:43:37,873 - __main__ - INFO - 🔇 Cooldown period active (3s after response), buffering speech...
2025-09-08 15:43:38,858 - __main__ - INFO - 🎯 Processing audio buffer: 109760 bytes (6.9s)
2025-09-08 15:43:38,859 - __main__ - INFO - 📊 Speech duration: 3.0s, Silence duration: 0.0s
2025-09-08 15:43:38,859 - __main__ - INFO - 🎤 VAD detected speech: True
2025-09-08 15:43:38,861 - __main__ - INFO - ✅ Converted dialer audio for Gemini: 109760 -> 219562 bytes
2025-09-08 15:43:38,861 - __main__ - INFO - 🎤 Calling Vertex AI Gemini with audio input (size: 219562 bytes)
2025-09-08 15:43:38,862 - __main__ - INFO - 📋 Using conversation history with 3 messages
2025-09-08 15:43:41,132 - __main__ - INFO - ✅ ############## AI Response from Vertex AI (audio input): 'Hello! I can certainly help you with insurance-related queries. What kind of insurance are you interested in today?'
2025-09-08 15:43:41,133 - __main__ - INFO - 🔊 Converting to speech: 'Hello! I can certainly help you with insurance-related queries. What kind of insurance are you interested in today?'
2025-09-08 15:43:41,744 - __main__ - INFO - ✅ TTS completed (size: 111814 bytes)
2025-09-08 15:43:41,754 - __main__ - INFO - 📤 Sent complete TTS audio to dialer dialer_1757326404409
2025-09-08 15:43:41,754 - __main__ - INFO - 🕒 TTS duration: 7.0s, will reset to listening automatically
2025-09-08 15:43:41,755 - __main__ - INFO - 🕒 Cooldown period (3s) started for dialer_1757326404409
2025-09-08 15:43:41,757 - __main__ - INFO - 🛑 Customer speech detected while bot speaking - sending interrupt to dialer
2025-09-08 15:43:41,757 - __main__ - INFO - 🛑 Sent __INTERRUPT__ signal to dialer for dialer_1757326404409
2025-09-08 15:43:41,758 - __main__ - INFO - 🎤 Speech detected by VAD, starting collection...
2025-09-08 15:43:41,758 - __main__ - INFO - 🔇 Cooldown period active (3s after response), buffering speech...
2025-09-08 15:43:42,775 - __main__ - INFO - 🔇 Cooldown period active (3s after response), buffering speech...
2025-09-08 15:43:43,780 - __main__ - INFO - 🔇 Cooldown period active (3s after response), buffering speech...
2025-09-08 15:43:44,773 - __main__ - INFO - 🎯 Processing audio buffer: 94720 bytes (5.9s)
2025-09-08 15:43:44,773 - __main__ - INFO - 📊 Speech duration: 3.0s, Silence duration: 0.0s
2025-09-08 15:43:44,774 - __main__ - INFO - 🎤 VAD detected speech: True
2025-09-08 15:43:44,775 - __main__ - INFO - ✅ Converted dialer audio for Gemini: 94720 -> 189482 bytes
2025-09-08 15:43:44,776 - __main__ - INFO - 🎤 Calling Vertex AI Gemini with audio input (size: 189482 bytes)
2025-09-08 15:43:44,776 - __main__ - INFO - 📋 Using conversation history with 5 messages
2025-09-08 15:43:47,560 - __main__ - INFO - ✅ ############## AI Response from Vertex AI (audio input): 'Great! Let me connect you with one of our insurance experts who can help you find the perfect policy. Please hold on.'
2025-09-08 15:43:47,561 - __main__ - INFO - 🔊 Converting to speech: 'Great! Let me connect you with one of our insurance experts who can help you find the perfect policy. Please hold on.'
2025-09-08 15:43:48,349 - __main__ - INFO - ✅ TTS completed (size: 116060 bytes)
2025-09-08 15:43:48,359 - __main__ - INFO - 📤 Sent complete TTS audio to dialer dialer_1757326404409
2025-09-08 15:43:48,360 - __main__ - INFO - 🕒 TTS duration: 7.3s, will reset to listening automatically
2025-09-08 15:43:48,360 - __main__ - INFO - 🕒 Cooldown period (3s) started for dialer_1757326404409
2025-09-08 15:43:48,360 - __main__ - INFO - 🎯 POSITIVE INTENT detected - connecting to agent
2025-09-08 15:43:48,360 - __main__ - INFO - 🔒 Session dialer_1757326404409 marked as 'agent_connected' - stopping audio processing
2025-09-08 15:43:48,361 - __main__ - INFO - 👨‍💼 Sent connect agent signal for dialer_1757326404409
2025-09-08 15:43:48,363 - __main__ - INFO - 🚫 Session dialer_1757326404409 state is 'agent_connected', ignoring audio
2025-09-08 15:43:48,363 - __main__ - INFO - 🚫 Session dialer_1757326404409 state is 'agent_connected', ignoring audio
2025-09-08 15:43:48,364 - __main__ - INFO - 🚫 Session dialer_1757326404409 state is 'agent_connected', ignoring audio
2025-09-08 15:43:48,364 - __main__ - INFO - 🚫 Session dialer_1757326404409 state is 'agent_connected', ignoring audio
2025-09-08 15:43:48,365 - __main__ - INFO - 🚫 Session dialer_1757326404409 state is 'agent_connected', ignoring audio
2025-09-08 15:43:48,365 - __main__ - INFO - 🚫 Session dialer_1757326404409 state is 'agent_connected', ignoring audio
2025-09-08 15:43:48,365 - __main__ - INFO - 🚫 Session dialer_1757326404409 state is 'agent_connected', ignoring audio
